<?php

namespace app\common\model;

use think\Model;

/**
 * 紧急联系人模型
 * 对应Java中的EmergencyContact实体类
 */
class EmergencyContact extends Model
{
    // 表名
    protected $table = 'emergency_contact';
    
    // 主键
    protected $pk = 'id';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    
    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    /**
     * 根据用户ID查询用户信息
     * 对应Java中EmergencyContactMapper.queryUserInfo方法
     * SQL: select linkman_name `name`,linkman_phone phone from emergency_contact where user_id = #{userId} and del_flag = 0
     * 
     * @param int $userId 用户ID
     * @return array|null 用户信息
     */
    public static function queryUserInfo($userId)
    {
        $result = self::field(['linkman_name as name', 'linkman_phone as phone'])
            ->where('user_id', $userId)
            ->where('del_flag', 0)
            ->find();
            
        return $result ? $result->toArray() : null;
    }

    /**
     * 更新或保存用户手机号
     * 对应Java中EmergencyContactMapper.updateEmergencyPhone方法
     * 
     * @param string $phone 手机号
     * @param string $userName 用户名
     * @param int $userId 用户ID
     * @return int 影响行数
     */
    public static function updateEmergencyPhone($phone, $userName = null, $userId)
    {
        $data = ['linkman_phone' => $phone];
        if (!empty($userName)) {
            $data['linkman_name'] = $userName;
        }
        
        return self::where('user_id', $userId)->update($data);
    }

    /**
     * 保存紧急联系人信息
     * 对应Java中EmergencyContactMapper.saveTable方法
     * 
     * @param array $contactData 联系人数据
     * @return int 新增记录的ID
     */
    public static function saveTable($contactData)
    {
        $contact = new self();
        $contact->data($contactData);
        $contact->save();
        
        return $contact->id;
    }

    /**
     * 更新或保存用户信息（先尝试更新，失败则新增）
     * 对应Java中EmergencyContactServiceImpl.updateOrSave方法
     * 
     * @param int $userId 用户ID
     * @param string $phone 手机号
     * @param string $name 姓名
     * @return int 影响行数或新增ID
     */
    public static function updateOrSave($userId, $phone, $name = null)
    {
        // 先尝试更新
        $num = self::updateEmergencyPhone($phone, $name, $userId);
        
        // 如果更新失败（记录不存在），则新增
        if ($num == 0) {
            $contactData = [
                'user_id' => $userId,
                'linkman_phone' => $phone,
                'del_flag' => 0
            ];
            
            if (!empty($name)) {
                $contactData['linkman_name'] = $name;
            }
            
            $num = self::saveTable($contactData);
        }
        
        return $num;
    }
}
