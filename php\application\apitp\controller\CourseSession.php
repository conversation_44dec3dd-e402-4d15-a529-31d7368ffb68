<?php

namespace app\apitp\controller;

use app\common\controller\Api;
use app\common\model\CourseSession as CourseSessionModel;
use app\common\model\CourseSubscribe;
use app\common\model\UserLinkman;
use app\common\model\EmergencyContact;
use app\apitp\library\RsaUtils;
use think\Cache;
use think\Db;
use think\Validate;

/**
 * 课程场次管理接口
 */
class CourseSession extends Api
{
    // 所有接口都需要登录
    protected $noNeedLogin = [];
    // 所有接口都需要验证权限
    protected $noNeedRight = '*';

  
    /**
     * 获取课程场次详情 - 对应Java版本getSessionDetail方法
     * @ApiTitle    (获取课程场次详情)
     * @ApiSummary  (根据指定日期查询当天的课程场次信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/session/getSessionDetail)
     * @ApiParams   (name="date", type="string", required=true, description="查询日期，格式：yyyy-MM-dd")
     */
    public function getSessionDetail()
    {
        // 获取请求参数
        $requestData = $this->request->post();
        $date = isset($requestData['date']) ? $requestData['date'] : '';

        // 参数验证 
        if (empty($date)) {
            return $this->error('时间不能为空');
        }

        // 验证日期格式
        if (!$this->validateDate($date)) {
            return $this->error('日期格式错误，请使用yyyy-MM-dd格式');
        }

        // 验证日期不能是过去的时间
        $inputDate = strtotime($date);
        $today = strtotime(date('Y-m-d'));
        if ($inputDate < $today) {
            return $this->error('课程已过期,无法查询!');
        }

        // 尝试从缓存获取数据 
        $cacheKey = 'course_state:' . $date;
        $cacheData = Cache::get($cacheKey);

        if ($cacheData) {
            $list = json_decode($cacheData, true);
        } else {
            // 查询数据库 \
            $list = $this->getCourseSessionsByDate($date);

            // 如果有数据才缓存
            if ($list && count($list) > 0) {
                // 缓存数据，3小时过期 
                Cache::set($cacheKey, json_encode($list), 10800);
            } else {
                // 返回空数组 
                $list = [];
            }
        }

        return $this->success('操作成功', $list);
    }

    /**
     * 根据日期查询课程场次数据 
     * @param string $date 查询日期
     * @return array
     */
    private function getCourseSessionsByDate($date)
    {
        // 使用原生SQL查询避免参数混合问题
        $sql = "SELECT id, course_cover as courseCover, course_name as courseName,
                       course_age_prop as courseAgeProp, inventory_votes as inventoryVotes,
                       course_start_time as courseStartTime, course_end_time as courseEndTime,
                       course_address as courseAddress, `week`
                FROM course_session
                WHERE date_format(course_start_time,'%y%m%d') = date_format(?, '%y%m%d')
                  AND del_flag = 0 AND course_state = 1
                ORDER BY course_start_time ASC";

        $result = Db::query($sql, [$date]);

        if (!$result) {
            return [];
        }

        $list = $result;

        // 格式化时间字段
        foreach ($list as &$item) {
            if (isset($item['courseStartTime'])) {
                $item['courseStartTime'] = date('Y/m/d H:i:s', strtotime($item['courseStartTime']));
            }
            if (isset($item['courseEndTime'])) {
                $item['courseEndTime'] = date('Y/m/d H:i:s', strtotime($item['courseEndTime']));
            }
            // 确保inventoryVotes是整数类型
            $item['inventoryVotes'] = (int)$item['inventoryVotes'];
            // 确保id是整数类型
            $item['id'] = (int)$item['id'];
        }

        return $list;
    }

    /**
     * 验证日期格式
     * @param string $date 日期字符串
     * @return bool
     */
    private function validateDate($date)
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * 显示我的报名记录
     * @ApiTitle    (显示我的报名记录)
     * @ApiSummary  (获取用户的课程报名和签到记录，支持分页)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/showMySign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="pageNum", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="pageSize", type="integer", required=false, description="每页数量，默认10")
     */
    public function showMySign()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 获取分页参数 
        $pageNum = $this->request->param('pageNum', 1, 'intval');
        $pageSize = $this->request->param('pageSize', 10, 'intval');

        // 查询总数 
        $total = $this->getMySignedCount($userId);
        $list = [];

        if ($total > 0) {
            // 计算分页参数 
            $start = ($pageNum - 1) * $pageSize;
            $end = $pageSize;

            // 查询分页数据
            $list = $this->getMySignList($userId, $start, $end);

            // 处理过期状态 
            $currentTime = time();
            foreach ($list as &$item) {
                // 如果flag为"1"且当前时间已超过课程结束时间，将flag更新为"2"
                if ($item['flag'] == '1' && $currentTime > strtotime($item['courseEndTime'])) {
                    $item['flag'] = '2';
                }
            }
        }

        // 返回结果 
        $result = [
            'total' => $total,
            'rows' => $list
        ];

        return $this->success('获取成功', $result);
    }




    /**
     * 获取用户课程报名记录总数
     * @param int $userId 用户ID
     * @return int
     */
    private function getMySignedCount($userId)
    {
       
        $sql = "SELECT COUNT(*) as count FROM (
                    SELECT course_session_id, user_id
                    FROM course_subscribe
                    WHERE user_id = ?
                    GROUP BY course_session_id, user_id, sign_state, del_flag
                ) tem";

        $result = Db::query($sql, [$userId]);
        return $result[0]['count'] ?? 0;
    }

    /**
     * 获取用户课程报名记录列表
     * @param int $userId 用户ID
     * @param int $start 偏移量
     * @param int $end 每页数量
     * @return array
     */
    private function getMySignList($userId, $start, $end)
    {

        $sql = "SELECT c.id as courseId, c.course_age_prop as courseAgeProp, c.course_cover as courseCover,
                       c.course_start_time as courseStartTime, c.course_end_time as courseEndTime,
                       c.week as weeks, c.course_name as courseName, c.course_address as courseAddress,
                       s.sign_state as signState, c.week, IF(s.del_flag = '0', '1', '0') as flag,
                       s.course_session_id, s.user_id, COUNT(l.linkman_name) as peopleCount
                FROM course_subscribe s
                LEFT JOIN course_session c ON c.id = s.course_session_id
                LEFT JOIN user_linkman l ON l.id = s.user_linkman_id
                WHERE s.user_id = ?
                GROUP BY c.course_start_time, c.course_end_time, c.course_name, c.course_address,
                         s.sign_state, c.week, IF(s.del_flag = '0', '1', '0'), s.course_session_id, s.user_id
                ORDER BY IF(s.del_flag = '0', '1', '0') DESC, c.course_start_time DESC
                LIMIT ?, ?";

        $result = Db::query($sql, [$userId, $start, $end]);

        // 格式化数据
        foreach ($result as &$item) {
            // 时间格式化 
            if ($item['courseStartTime']) {
                $item['courseStartTime'] = date('Y/m/d H:i:s', strtotime($item['courseStartTime']));
            }
            if ($item['courseEndTime']) {
                $item['courseEndTime'] = date('Y/m/d H:i:s', strtotime($item['courseEndTime']));
            }

            // 确保peopleCount是字符串类型
            $item['peopleCount'] = (string)$item['peopleCount'];
        }

        return $result;
    }

    /**
     * 显示课程凭证
     * @ApiTitle    (显示课程凭证)
     * @ApiSummary  (显示用户在指定课程的预约凭证信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/showVoucher)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     */
    public function showVoucher()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 获取课程ID参数
        $courseId = $this->request->param('courseId', 0, 'intval');
        if (!$courseId) {
            return $this->error('课程ID不能为空');
        }

        // 调用业务逻辑生成课程凭证
        $voucher = $this->getCourseVoucher($courseId, $userId);

        return $this->success('获取成功', $voucher);

      
    }

    /**
     * 获取课程凭证业务逻辑
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @return array
     * @throws \Exception
     */
    private function getCourseVoucher($courseId, $userId)
    {
        // 1. 验证课程是否过期
        $courseSession = CourseSessionModel::where('id', $courseId)->find();
        if (!$courseSession) {
            return $this->error('该课程不存在');
        }

        $now = strtotime(date('Y-m-d'));
        $courseStartTime = strtotime(date('Y-m-d', strtotime($courseSession['course_start_time'])));

        if ($now > $courseStartTime) {
            return $this->error('已过期无法查询');
        }

        // 2. 查询用户是否有该课程的预约记录
        $subscribeList = CourseSubscribe::where('course_session_id', $courseId)
            ->where('user_id', $userId)
            ->where('del_flag', '0')
            ->select();

        if (!$subscribeList || count($subscribeList) == 0) {
            return $this->error('未查询到有效记录!或科技馆当天已关闭');
        }

        // 3. 查询课程凭证详细信息
        $voucherData = $this->getCourseVoucherDetail($courseId, $userId);

        if (!$voucherData) {
            return $this->error('未查询到凭证信息');
        }

        // 4. 设置签到状态
        $voucherData['signState'] = $subscribeList[0]['sign_state'];

        // 5. 对姓名和手机号进行隐私处理
        if (!empty($voucherData['name'])) {
            $voucherData['name'] = desensitize_name($voucherData['name']);
        }
        if (!empty($voucherData['phone'])) {
            $voucherData['phone'] = desensitize_phone($voucherData['phone']);
        }

        // 6. 对人员列表进行隐私处理
        if (isset($voucherData['peoples']) && is_array($voucherData['peoples'])) {
            foreach ($voucherData['peoples'] as &$people) {
                if (!empty($people['linkmanName'])) {
                    $people['linkmanName'] = desensitize_name($people['linkmanName']);
                }
                if (!empty($people['linkmanCertificate'])) {
                    $people['linkmanCertificate'] = desensitize_certificate($people['linkmanCertificate']);
                }
                if (!empty($people['linkmanPhone'])) {
                    $people['linkmanPhone'] = desensitize_phone($people['linkmanPhone']);
                }
            }
        }

        return $voucherData;
    }

    /**
     * 获取课程凭证详细信息
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @return array|null
     */
    private function getCourseVoucherDetail($courseId, $userId)
    {
        // 执行联表查询获取凭证信息
        $sql = "SELECT c.week, c.course_start_time, c.course_cover, c.course_introduce, c.course_name,
                       c.course_age_prop, c.course_address, c.course_end_time, l.linkman_name,
                       l.linkman_certificate, l.linkman_age, l.linkman_phone as linkmanPhone,
                       ec.linkman_name as name, ec.linkman_phone as phonenumber, l.id as linkId
                FROM course_session c
                LEFT JOIN course_subscribe s ON c.id = s.course_session_id AND s.user_id = ? AND s.del_flag = '0'
                JOIN user_linkman l ON l.id = s.user_linkman_id
                JOIN emergency_contact ec ON ec.user_id = s.user_id
                WHERE c.id = ?";

        $result = Db::query($sql, [$userId, $courseId]);

        if (!$result || count($result) == 0) {
            return null;
        }

        // 组装返回数据
        $firstRecord = $result[0];
        $voucher = [
            'name' => $firstRecord['name'] ?? '',
            'phone' => $firstRecord['phonenumber'] ?? '',
            'week' => (int)$firstRecord['week'],
            'signState' => '', // 将在上层设置
            'courseCover' => $firstRecord['course_cover'] ?? '',
            'courseName' => $firstRecord['course_name'] ?? '',
            'courseAddress' => $firstRecord['course_address'] ?? '',
            'courseIntroduce' => $firstRecord['course_introduce'] ?? '',
            'courseAgeProp' => $firstRecord['course_age_prop'] ?? '',
            'courseStartTime' => date('Y/m/d H:i:s', strtotime($firstRecord['course_start_time'])),
            'courseEndTime' => date('Y/m/d H:i:s', strtotime($firstRecord['course_end_time'])),
            'qrCode' => '', // 二维码字段，暂时为空
            'peoples' => []
        ];

        // 组装人员信息
        foreach ($result as $item) {
            $voucher['peoples'][] = [
                'linkId' => (int)$item['linkId'],
                'linkmanName' => $item['linkman_name'] ?? '',
                'linkmanCertificate' => $item['linkman_certificate'] ?? '',
                'linkmanAge' => (int)$item['linkman_age'],
                'linkmanPhone' => $item['linkmanPhone'] ?? ''
            ];
        }

        return $voucher;
    }

    /**
     * 取消课程预约
     * @ApiTitle    (取消课程预约)
     * @ApiSummary  (取消用户在指定课程的预约记录)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/cancelSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     * @ApiParams   (name="peopleIds", type="array", required=true, description="人员ID数组")
     */
    public function cancelSign()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 获取参数
        $courseId = $this->request->param('courseId', 0, 'intval');
        $peopleIds = $this->request->param('peopleIds', [], 'array');
        if (!$courseId) {
            return $this->error('课程ID不能为空');
        }
        if (empty($peopleIds)) {
            return $this->error('人员ID不能为空');
        }

        // 转换为整数数组
        $peopleIds = array_map('intval', $peopleIds);

        // 调用取消预约业务逻辑
        $result = $this->doCancelSign($userId, $courseId, $peopleIds);
        return $this->success('取消成功');
       
    }

    /**
     * 执行取消预约业务逻辑 - 对应Java中CourseSessionServiceImpl.cancelSign方法
     * @param int $userId 用户ID
     * @param int $courseId 课程ID
     * @param array $peopleIds 人员ID数组
     * @return bool|string true表示成功，字符串表示错误信息
     */
    private function doCancelSign($userId, $courseId, $peopleIds)
    {
        // 1. 验证用户是否有该课程的预约记录且未完成签到 
        $stoke = $this->verifyHadSigned($courseId, $userId);
        if ($stoke <= 0) {
            return $this->error('取消失败,未预约课程或已完成签到!');
        }

        // 2. 如果指定了人员ID且数量小于总预约数，则只取消指定人员的预约
        if (!empty($peopleIds) && $stoke > count($peopleIds)) {
            $stoke = count($peopleIds);
        }

        // 3. 获取课程信息并检查时间
        $courseSession = CourseSessionModel::where('id', $courseId)->find();
        if (!$courseSession) {
            return $this->error('课程不存在');
        }

        $now = time();
        $courseStartTime = strtotime($courseSession['course_start_time']);
        if ($now > $courseStartTime) {
            return $this->error('课程开始时间已过,无法取消!');
        }

        // 4. 开始数据库事务
        Db::startTrans();

        // 5. 恢复课程库存 - 对应Java中updateVote操作
        $this->updateCourseInventory($courseId, $stoke);

        // 6. 删除预约记录 - 对应Java中deleteRecord操作
        $this->deleteSubscribeRecords($userId, $courseId, $peopleIds);

        // 7. 异步更新缓存 - 对应Java中CompletableFuture.runAsync
        $this->updateCourseCacheAsync($courseSession, $stoke);

        Db::commit();
        return true;
      
    }

    /**
     * 验证用户是否有该课程的预约记录且未完成签到 - 对应Java中verifyHadSigned
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @return int 预约记录数量
     */
    private function verifyHadSigned($courseId, $userId)
    {

        return CourseSubscribe::where('user_id', $userId)
            ->where('course_session_id', $courseId)
            ->where('del_flag', '0')
            ->where('sign_state', '0')
            ->count();
    }

    /**
     * 更新课程库存 - 对应Java中updateVote操作
     * @param int $courseId 课程ID
     * @param int $addCount 增加的库存数量
     */
    private function updateCourseInventory($courseId, $addCount)
    {

        CourseSessionModel::where('id', $courseId)
            ->setInc('inventory_votes', $addCount);
    }

    /**
     * 删除预约记录 - 对应Java中deleteRecord操作
     * @param int $userId 用户ID
     * @param int $courseId 课程ID
     * @param array $peopleIds 人员ID数组
     */
    private function deleteSubscribeRecords($userId, $courseId, $peopleIds)
    {
        // 对应Java中removeSign的SQL逻辑
        foreach ($peopleIds as $linkId) {
            CourseSubscribe::where('user_id', $userId)
                ->where('course_session_id', $courseId)
                ->where('user_linkman_id', $linkId)
                ->delete();
        }
    }

    /**
     * 异步更新课程缓存 - 对应Java中CompletableFuture.runAsync缓存更新逻辑
     * @param array $courseSession 课程信息
     * @param int $finalStoke 恢复的库存数量
     */
    private function updateCourseCacheAsync($courseSession, $finalStoke)
    {
      
        // 对应Java中的缓存key格式
        $keyDate = 'course_state:' . date('Y-m-d', strtotime($courseSession['course_start_time']));
        $keyId = 'course_state:' . $courseSession['id'];

        // 更新日期缓存
        $cacheList = Cache::get($keyDate);
        if (!empty($cacheList)) {
            $webCourseResultVos = json_decode($cacheList, true);
            if ($webCourseResultVos && is_array($webCourseResultVos)) {
                foreach ($webCourseResultVos as &$webCourseResultVo) {
                    if ($webCourseResultVo['id'] == $courseSession['id']) {
                        $webCourseResultVo['inventoryVotes'] += $finalStoke;
                        break;
                    }
                }
                // 缓存3小时
                Cache::set($keyDate, json_encode($webCourseResultVos), 10800);
            }
        }

        // 更新详情缓存
        $cacheDetail = Cache::get($keyId);
        if (!empty($cacheDetail)) {
            $courseResultVo = json_decode($cacheDetail, true);
            if ($courseResultVo && is_array($courseResultVo)) {
                $courseResultVo['inventoryVotes'] += $finalStoke;
                // 缓存3小时
                Cache::set($keyId, json_encode($courseResultVo), 10800);
            }
        }
      
    }

    /**
     * 课程签到
     * @ApiTitle    (课程签到)
     * @ApiSummary  (用户在指定课程进行签到)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/centerSign)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     */
    public function centerSign()
    {
        // 获取用户ID
        $userId = $this->getUserId();

        // 获取参数
        $courseId = $this->request->param('courseId', 0, 'intval');

        if (!$courseId) {
            return $this->error('课程ID不能为空');
        }

      
        // 调用签到业务逻辑
        $result = $this->doCenterSign($courseId, $userId);

        if ($result > 0) {
            return $this->success('签到成功');
        } else {
            return $this->error('签到失败');
        }
      
    }

    /**
     * 执行课程签到业务逻辑 - 对应Java中CourseSessionServiceImpl.centerSign方法
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @return int 影响行数
     */
    private function doCenterSign($courseId, $userId)
    {
        // 1. 获取课程信息
        $courseSession = CourseSessionModel::where('id', $courseId)->find();
        if (!$courseSession) {
            return $this->error('课程不存在');
        }

        // 2. 查询用户预约记录
        $courseSubscribeList = $this->selectUserRecord($courseId, $userId);

        if (!empty($courseSubscribeList)) {
            $courseSubscribeList = collection( $courseSubscribeList)->toArray();
            // 检查是否已签到
            if ($courseSubscribeList[0]['sign_state'] == '1') {
                return $this->error('已签到,无需再次签到!');
            }
        } else {
            return $this->error('未获取到有效数据');
        }

        // 3. 时间检查
        $this->validateSignTime($courseSession);

        // 4. 执行签到
        return $this->updateSignState($courseId, $userId);
    }

    /**
     * 查询用户预约记录 - 对应Java中selectUserRecord
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @return array 预约记录列表
     */
    private function selectUserRecord($courseId, $userId)
    {

        return CourseSubscribe::where('user_id', $userId)
            ->where('course_session_id', $courseId)
            ->where('del_flag', '0')
            ->select();
    }

    /**
     * 验证签到时间 - 对应Java中的时间检查逻辑
     * @param array $courseSession 课程信息
     * @throws \Exception
     */
    private function validateSignTime($courseSession)
    {
        $now = time();

     
        $courseStartTime = strtotime($courseSession['course_start_time']);
        $beginOfDay = strtotime(date('Y-m-d 00:00:00', $courseStartTime));

        //上课开始10分支结束签到
        $courseEndCheck = 10;

    
        $endOfDay = $courseStartTime + $courseEndCheck * 60;
        if ($now > $beginOfDay && $now < $endOfDay) {
            return; // 在允许签到的时间范围内
        }
        $this->error('不在规定的时间内无法签到!');
    }

    /**
     * 更新签到状态 - 对应Java中centerSign SQL操作
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @return int 影响行数
     */
    private function updateSignState($courseId, $userId)
    {

        return CourseSubscribe::where('user_id', $userId)
            ->where('course_session_id', $courseId)
            ->where('del_flag', '0')
            ->update(['sign_state' => '1']);
    }

    /**
     * 获取课程详情 - 对应Java版本getDetail方法
     * @ApiTitle    (获取课程详情)
     * @ApiSummary  (根据课程ID获取课程的详细信息)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/getDetail/{id})
     * @ApiParams   (name="id", type="integer", required=true, description="课程ID")
     */
    public function getDetail()
    {
        // 获取课程ID参数
        $id = $this->request->param('id', 0, 'intval');

        if (!$id) {
            return $this->error('课程ID不能为空');
        }

        // 调用业务逻辑获取课程详情
        $courseDetail = $this->getCourseDetail($id);

        if (!$courseDetail) {
            return $this->error('课程不存在');
        }

        return $this->success('获取成功', $courseDetail);
    }

    /**
     * 获取课程详情业务逻辑 - 对应Java中CourseSessionServiceImpl.getDetail方法
     * @param int $id 课程ID
     * @return array|null
     */
    private function getCourseDetail($id)
    {
        // 1. 尝试从缓存获取数据 
        $cacheKey = 'course_state:' . $id;
        $cacheData = Cache::get($cacheKey);

        if ($cacheData) {
            return json_decode($cacheData, true);
        }

        // 2. 从数据库查询课程信息 
        $courseSession = $this->selectCourseSessionById($id);

        if (!$courseSession) {
            return null;
        }

        // 3. 转换为CourseResultVo格式 
        $courseResultVo = $this->convertToCourseResultVo($courseSession);

        // 4. 缓存数据，3小时过期 
        Cache::set($cacheKey, json_encode($courseResultVo), 10800);

        return $courseResultVo;
    }

    /**
     * 根据ID查询课程信息 - 对应Java中CourseSessionMapper.selectCourseSessionById
     * 使用与Java完全相同的SQL查询逻辑
     * @param int $id 课程ID
     * @return array|null
     */
    private function selectCourseSessionById($id)
    {
        // 对应Java中的selectCourseSessionVo SQL片段
        $result = CourseSessionModel::field([
            'id',
            'course_number',
            'course_cover',
            'course_introduce',
            'course_name',
            'course_age_prop',
            'course_poll',
            'course_address',
            'course_state',
            'course_start_time',
            'course_end_time',
            'week',
            'inventory_votes',
            'create_by',
            'create_time',
            'update_by',
            'update_time',
            'remark',
            'del_flag'
        ])
        ->where('id', $id)
        ->find();

        return $result ? $result->toArray() : null;
    }

    /**
     * 转换为CourseResultVo格式 - 对应Java中的CourseResultVo类
     * @param array $courseSession 课程信息
     * @return array
     */
    private function convertToCourseResultVo($courseSession)
    {
        return [
            'id' => (int)$courseSession['id'],
            'courseIntroduce' => $courseSession['course_introduce'] ?? null,
            'courseCover' => $courseSession['course_cover'] ?? '',
            'courseName' => $courseSession['course_name'] ?? '',
            'courseAgeProp' => $courseSession['course_age_prop'] ?? '',
            'coursePoll' => (int)$courseSession['course_poll'],
            'courseAddress' => $courseSession['course_address'] ?? null,
            'courseState' => $courseSession['course_state'] ?? '',
            'courseStartTime' => $courseSession['course_start_time'] ? date('Y/m/d H:i:s', strtotime($courseSession['course_start_time'])) : null,
            'courseEndTime' => $courseSession['course_end_time'] ? date('Y/m/d H:i:s', strtotime($courseSession['course_end_time'])) : null,
            'week' => $courseSession['week'] ?? null,
            'inventoryVotes' => (int)$courseSession['inventory_votes']
        ];
    }

    /**
     * 获取用户手机号信息 - 对应Java版本getPhone接口
     * @ApiTitle    (获取用户手机号信息)
     * @ApiSummary  (获取当前用户的姓名和手机号信息，手机号会进行RSA加密)
     * @ApiMethod   (GET)
     * @ApiRoute    (/web/session/getPhone)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     */
    public function getPhone()
    {
       
        // 获取用户ID 
        $userId = $this->getUserId();

        // 获取用户信息 
        $userInfo = $this->getUserInfo($userId);

        if ($userInfo && !empty($userInfo)) {
            // 对手机号进行RSA公钥加密
            if (!empty($userInfo['phone'])) {
                $userInfo['phone'] = RsaUtils::encryptByPublicKey(RsaUtils::$publicKey, $userInfo['phone']);
            }
            // 对应Java中AjaxResult.success((Object)userInfo)
            return $this->success('操作成功', $userInfo);
        }

        // 对应Java中AjaxResult.success((Object)new UserInfo()) - 返回空的UserInfo对象
        return $this->success('操作成功', ['name' => '', 'phone' => '']);

    }

    /**
     * 保存用户手机号 - 对应Java版本saveUserPhone接口
     * @ApiTitle    (保存用户手机号)
     * @ApiSummary  (保存用户的姓名和手机号信息，手机号需要RSA解密)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/session/saveUserPhone)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="name", type="string", required=true, description="用户姓名")
     * @ApiParams   (name="phone", type="string", required=true, description="加密后的手机号")
     */
    public function saveUserPhone()
    {
       
            // 获取用户ID
        $userId = $this->getUserId();

        // 获取请求参数 
        // 支持JSON和表单数据两种格式
        $contentType = $this->request->header('content-type');
        if (strpos($contentType, 'application/json') !== false) {
            // JSON数据处理
            $requestData = json_decode($this->request->getContent(), true);
            if (!$requestData) {
                return $this->error('JSON数据格式错误');
            }
        } else {
            // 表单数据处理
            $requestData = $this->request->post();
        }
    
        $name = isset($requestData['name']) ? trim($requestData['name']) : '';
        $encryptedPhone = isset($requestData['phone']) ? trim($requestData['phone']) : '';

        // 参数验证
        if (empty($name)) {
            return $this->error('姓名不能为空');
        }
        if (empty($encryptedPhone)) {
            return $this->error('手机号不能为空');
        }

        // 对手机号进行RSA私钥解密 
        $phone = RsaUtils::decryptByPrivateKey($encryptedPhone);
        // 保存用户手机号 
        $result = $this->saveUserPhoneInfo($userId, $name, $phone);

        return $this->success('操作成功', $result);

    }

    /**
     * 获取用户信息业务逻辑 - 对应Java中CourseSessionServiceImpl.getUserInfo方法
     * @param int $userId 用户ID
     * @return array|null 用户信息
     */
    private function getUserInfo($userId)
    {
       
        return EmergencyContact::queryUserInfo($userId);
    }

    /**
     * 保存用户手机号业务逻辑 - 对应Java中CourseSessionServiceImpl.saveUserPhone方法
     * @param int $userId 用户ID
     * @param string $name 用户姓名
     * @param string $phone 手机号
     * @return int 影响行数
     */
    private function saveUserPhoneInfo($userId, $name, $phone)
    {
      
        return EmergencyContact::updateOrSave($userId, $phone, $name);
    }

    /**
     * 课程预约报名 - 对应Java版本signCourse接口
     * @ApiTitle    (课程预约报名)
     * @ApiSummary  (用户预约指定课程，支持多人预约)
     * @ApiMethod   (POST)
     * @ApiRoute    (/web/session/signCourse)
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="courseId", type="integer", required=true, description="课程ID")
     * @ApiParams   (name="userId", type="integer", required=false, description="用户ID，可选，默认使用当前登录用户")
     * @ApiParams   (name="linkIds", type="array", required=true, description="联系人ID数组")
     */
    public function signCourse()
    {
        // 获取请求参数
        $contentType = $this->request->header('content-type');
        if (strpos($contentType, 'application/json') !== false) {
            // JSON数据处理
            $requestData = json_decode($this->request->getContent(), true);
            if (!$requestData) {
                return $this->error('JSON数据格式错误');
            }
        } else {
            // 表单数据处理
            $requestData = $this->request->post();
        }

        $courseId = isset($requestData['courseId']) ? intval($requestData['courseId']) : 0;
        $userId = isset($requestData['userId']) ? intval($requestData['userId']) : null;
        $linkIds = isset($requestData['linkIds']) ? $requestData['linkIds'] : [];

        // 如果没有传userId，使用当前登录用户ID
        if (!$userId) {
            $userId = $this->getUserId();
        }
        // 参数验证
        if (!$courseId) {
            return $this->error('课程ID不能为空');
        } 
        if (empty($linkIds) || !is_array($linkIds)) {
            return $this->error('联系人不能为空!');
        }

        // 转换为整数数组
        $linkIds = array_map('intval', $linkIds);
        $linkIds = array_filter($linkIds); // 过滤掉0值

        if (empty($linkIds)) {
            return $this->error('联系人不能为空!');
        }

        // 调用业务逻辑
        $result = $this->doSignCourse($courseId, $userId, $linkIds);

        if ($result === true) {
            return $this->success('预约成功');
        } else {
            return $this->error($result);
        }
    }

    /**
     * 执行课程预约业务逻辑 - 对应Java中CourseSessionServiceImpl.signCourse方法
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @param array $linkIds 联系人ID数组
     * @return bool|string true表示成功，字符串表示错误信息
     */
    private function doSignCourse($courseId, $userId, $linkIds)
    {
        $size = count($linkIds);

        // 1. 获取课程信息并验证
        $courseSession = CourseSessionModel::where('id', $courseId)->find();
        if (!$courseSession) {
            return '该场次不存在,无法预约!';
        }

        // 验证课程删除状态 
        if ($courseSession['del_flag'] == '2') {
            return '该场次不存在,无法预约!';
        }

        // 验证课程预约状态 
        if ($courseSession['course_state'] == '0') {
            return '该场次暂时为不可预约状态,请联系管理员开启!';
        }

        // 2. 验证用户是否已报名 
        foreach ($linkIds as $linkId) {
            $count = $this->verifySignUp($courseId, $userId, $linkId);
            if ($count > 0) {
                return '您已报名/签到,如需更改预约人,请在未签到情况下取消重报!';
            }
        }

        Db::startTrans();


        // 重新获取课程信息以确保数据一致性
        $courseSession = CourseSessionModel::lock(true)->where('id', $courseId)->find();
        if (!$courseSession) {
            Db::rollback();
            return '该场次不存在,无法预约!';
        }

        $oldVotes = intval($courseSession['inventory_votes']);

        // 4. 检查库存是否足够
        if ($oldVotes < $size) {
            Db::rollback();
            return '余票不足请选择其他场次!';
        }

        // 5. 更新库存 
        $newVotes = $oldVotes - $size;
        $updateResult = CourseSessionModel::where('id', $courseId)
            ->where('del_flag', '0')
            ->update(['inventory_votes' => $newVotes]);

        if (!$updateResult) {
            Db::rollback();
            return '更新库存失败';
        }

        // 6. 保存预约记录
        $this->saveSubscribeRecord($courseId, $userId, $linkIds);

        Db::commit();

        // 7. 异步更新缓存
        $this->updateSignCourseCacheAsync($courseSession, $size);

        return true;

     
    }

    /**
     * 验证用户是否已报名 - 对应Java中CourseSubscribeMapper.verifySignUp
     * SQL: select count(*) from course_subscribe where user_id = #{userId} and course_session_id = #{courseId} and user_linkman_id = #{linkId} and del_flag = 0
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @param int $linkId 联系人ID
     * @return int 记录数量
     */
    private function verifySignUp($courseId, $userId, $linkId)
    {
        return CourseSubscribe::where('user_id', $userId)
            ->where('course_session_id', $courseId)
            ->where('user_linkman_id', $linkId)
            ->where('del_flag', '0')
            ->count();
    }

    /**
     * 保存预约记录 - 对应Java中saveRecord方法
     * @param int $courseId 课程ID
     * @param int $userId 用户ID
     * @param array $linkIds 联系人ID数组
     */
    private function saveSubscribeRecord($courseId, $userId, $linkIds)
    {
        $subscribeList = [];

        foreach ($linkIds as $linkId) {
            $subscribeList[] = [
                'user_id' => $userId,
                'course_session_id' => $courseId,
                'user_linkman_id' => $linkId,
                'subscribe_state' => '1', 
                'sign_state' => '0',      // 默认未签到
                'del_flag' => '0',        // 默认未删除
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s')
            ];
        }

        // 批量插入
        if (!empty($subscribeList)) {
            $courseSubscribeModel = new CourseSubscribe;
            $courseSubscribeModel->saveAll($subscribeList);
        }
    }

    /**
     * 异步更新课程缓存 - 对应Java中CompletableFuture.runAsync缓存更新逻辑
     * @param array $courseSession 课程信息
     * @param int $size 预约人数
     */
    private function updateSignCourseCacheAsync($courseSession, $size)
    {
        // 缓存key格式
        $keyDate = 'course_state:' . date('Y-m-d', strtotime($courseSession['course_start_time']));
        $keyId = 'course_state:' . $courseSession['id'];

        // 更新日期缓存 
        $cacheList = Cache::get($keyDate);
        if (!empty($cacheList)) {
            $webCourseResultVos = json_decode($cacheList, true);
            if ($webCourseResultVos && is_array($webCourseResultVos)) {
                foreach ($webCourseResultVos as &$webCourseResultVo) {
                    if ($webCourseResultVo['id'] == $courseSession['id']) {
                        $temStoke = $webCourseResultVo['inventoryVotes'] - $size;
                        $webCourseResultVo['inventoryVotes'] = $temStoke;
                        break;
                    }
                }
                // 缓存3小时
                Cache::set($keyDate, json_encode($webCourseResultVos), 10800);
            }
        }

        // 更新详情缓存
        $cacheDetail = Cache::get($keyId);
        if (!empty($cacheDetail)) {
            $courseResultVo = json_decode($cacheDetail, true);
            if ($courseResultVo && is_array($courseResultVo)) {
                $courseResultVo['inventoryVotes'] = $courseResultVo['inventoryVotes'] - $size;
                // 缓存3小时
                Cache::set($keyId, json_encode($courseResultVo), 10800);
            }
        }
    }

    /**
     * 获取当前用户ID
     */
    private function getUserId()
    {
        return   $this->auth->getUser()->user_id;
    }

}